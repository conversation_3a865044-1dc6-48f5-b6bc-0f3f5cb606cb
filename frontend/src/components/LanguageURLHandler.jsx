import { useEffect, useCallback, useRef } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';

/**
 * Component that handles URL-based language detection and updates
 * This component should be placed inside the Router but outside of Routes
 */
const LanguageURLHandler = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { currentLanguage, changeLanguageOnly, availableLanguages } = useLanguage();

  // Extract language from URL path
  const getLanguageFromPath = useCallback((pathname) => {
    const segments = pathname.split('/').filter(Boolean);
    const firstSegment = segments[0];

    // Check if first segment is a valid language code
    if (firstSegment && availableLanguages[firstSegment]) {
      return firstSegment;
    }
    return null;
  }, [availableLanguages]);

  // Add language prefix to path
  const addLanguageToPath = useCallback((pathname, language) => {
    const cleanPath = pathname === '/' ? '' : pathname;
    return `/${language}${cleanPath}`;
  }, []);

  // Update URL when language changes (from language selector)
  const updateURLForLanguageChange = useCallback((newLanguage, currentPath) => {
    const urlLanguage = getLanguageFromPath(currentPath);

    if (urlLanguage && urlLanguage !== newLanguage) {
      // Replace current language in URL with new language
      const segments = currentPath.split('/').filter(Boolean);
      segments[0] = newLanguage; // Replace first segment (language)
      const newPath = '/' + segments.join('/');
      navigate(newPath, { replace: true });
    } else if (!urlLanguage) {
      // Add language prefix to current path
      const newPath = addLanguageToPath(currentPath, newLanguage);
      navigate(newPath, { replace: true });
    }
  }, [getLanguageFromPath, addLanguageToPath, navigate]);

  // Use ref to track if we're in the middle of a language change to prevent loops
  const isChangingLanguage = useRef(false);

  // Handle URL changes (when user navigates or enters URL directly)
  useEffect(() => {
    if (isChangingLanguage.current) return;

    const urlLanguage = getLanguageFromPath(location.pathname);

    if (urlLanguage) {
      // URL has language prefix
      if (urlLanguage !== currentLanguage) {
        // Update context language to match URL
        isChangingLanguage.current = true;
        changeLanguageOnly(urlLanguage).finally(() => {
          isChangingLanguage.current = false;
        });
      }
    } else {
      // URL doesn't have language prefix, redirect to include current language
      const newPath = addLanguageToPath(location.pathname, currentLanguage);
      navigate(newPath, { replace: true });
    }
  }, [location.pathname, currentLanguage, getLanguageFromPath, addLanguageToPath, navigate, changeLanguageOnly]);

  // Handle language changes from context (language selector)
  useEffect(() => {
    if (isChangingLanguage.current) return;

    const urlLanguage = getLanguageFromPath(location.pathname);

    if (urlLanguage && urlLanguage !== currentLanguage) {
      // Language was changed via selector, update URL
      updateURLForLanguageChange(currentLanguage, location.pathname);
    }
  }, [currentLanguage, getLanguageFromPath, updateURLForLanguageChange, location.pathname]);

  // This component doesn't render anything
  return null;
};

export default LanguageURLHandler;
