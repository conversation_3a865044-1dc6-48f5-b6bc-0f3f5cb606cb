import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import PropTypes from 'prop-types';
import { AuthProvider } from './context/AuthContext';
import { LanguageProvider } from './context/LanguageContext';
import { SidebarProvider } from './context/SidebarContext';
import { GoogleOAuthProvider } from '@react-oauth/google';

// Pages
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import RegisterPage from './pages/RegisterPage';
import SearchPage from './pages/SearchPage';
import GamePage from './pages/GamePage';
import AboutPage from './pages/AboutPage';
import UploadGamePage from './pages/UploadGamePage';
import NotFoundPage from './pages/NotFoundPage';
import DiscordCallback from './pages/DiscordCallback';
import ProfilePage from './pages/ProfilePage';
import UserProfilePage from './pages/UserProfilePage';
import CategoryPage from './pages/CategoryPage';
import RecentlyPlayedPage from './pages/RecentlyPlayedPage';

import Header from './components/Header';
import Footer from './components/Footer';
import Sidebar from './components/Sidebar';
import { useSidebar } from './context/SidebarContext';

function AppContent({ children }) {
  const { isSidebarOpen, toggleSidebar } = useSidebar();

  return (
    <div className="min-h-screen bg-[#0d0d0d] text-white">
      <Header />
      <div className="flex flex-1">
        <Sidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
        <main className={`flex-1 ${isSidebarOpen ? 'lg:pl-64' : 'lg:pl-0'} overflow-x-hidden`}>
          {children}
        </main>
      </div>
      <Footer />
    </div>
  );
}

AppContent.propTypes = {
  children: PropTypes.node.isRequired
};

function App() {
  return (
    <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_CLIENT_ID}>
      <Router>
        <LanguageProvider>
          <AuthProvider>
            <SidebarProvider>
              <AppContent>
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/login" element={<LoginPage />} />
                  <Route path="/register" element={<RegisterPage />} />
                  <Route path="/search" element={<SearchPage />} />
                  <Route path="/about" element={<AboutPage />} />
                  <Route path="/upload-game" element={<UploadGamePage />} />
                  <Route path="/auth/discord/callback" element={<DiscordCallback />} />
                  <Route path="/profile" element={<ProfilePage />} />
                  <Route path="/user/:userId" element={<UserProfilePage />} />
                  <Route path="/game/:id" element={<GamePage />} />

                  {/* Navigation Routes */}
                  <Route path="/recently-played" element={<RecentlyPlayedPage />} />
                  <Route path="/new" element={<CategoryPage />} />
                  <Route path="/trending" element={<CategoryPage />} />
                  <Route path="/updated" element={<CategoryPage />} />
                  <Route path="/originals" element={<CategoryPage />} />
                  <Route path="/multiplayer" element={<CategoryPage />} />

                  {/* Category Routes - Single dynamic route */}
                  <Route path="/category/:category" element={<CategoryPage />} />

                  {/* Game by slug route - now at root level */}
                  <Route path="/:gameSlug" element={<GamePage usePath={true} />} />
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </AppContent>
            </SidebarProvider>
          </AuthProvider>
        </LanguageProvider>
      </Router>
    </GoogleOAuthProvider>
  );
}

export default App;
